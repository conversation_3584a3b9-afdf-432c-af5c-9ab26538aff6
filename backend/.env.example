# Copy this file to .env and fill in your values

# Environment Mode
# Valid values: local, staging, production
ENV_MODE=local

# DATABASE
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# LLM Providers
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
MODEL_TO_USE=

# Web Scraping
FIRECRAWL_API_KEY=

# Daytona Configuration
DAYTONA_API_KEY=
DAYTONA_SERVER_URL=
DAYTONA_TARGET=

# API Keys
TAVILY_API_KEY=
RAPID_API_KEY=
OPENROUTER_API_KEY=

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SSL=false

# OAuth Configuration
# Comma-separated list of allowed redirect URIs for OAuth callbacks
OAUTH_ALLOWED_REDIRECT_URIS=

# Session secret key for encrypting session data
# Generate a secure random key with: openssl rand -hex 32
SESSION_SECRET=

# Encryption Configuration
# Generate a secure random key with: openssl rand -hex 32
ENCRYPTION_KEY=
# Generate a secure random salt with: openssl rand -hex 16
ENCRYPTION_SALT=

# Browser Use API
BROWSER_USE_API_KEY=

# Composio Configuration
COMPOSIO_MCP_GMAIL_URL=
COMPOSIO_API_KEY=
COMPOSIO_ENTITY_ID=default
COMPOSIO_APP=GMAIL

# Composio MCP (SSE/JSON-RPC) Configuration
COMPOSIO_MCP_URL=

# Legacy/Additional Configuration
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=

GROQ_API_KEY=

LANGFUSE_PUBLIC_KEY="pk-REDACTED"
LANGFUSE_SECRET_KEY="sk-REDACTED"
LANGFUSE_HOST="https://cloud.langfuse.com"

SMITHERY_API_KEY=
